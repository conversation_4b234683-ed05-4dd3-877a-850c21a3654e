{"name": "deliverio-pickup-module", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:migrate": "prisma migrate dev", "prisma:generate": "prisma generate", "seed": "node prisma/seed.js"}, "dependencies": {"next": "14.2.5", "react": "18.3.1", "react-dom": "18.3.1", "zod": "3.23.8", "@prisma/client": "5.20.0", "clsx": "2.1.1"}, "devDependencies": {"typescript": "5.6.2", "@types/node": "22.7.4", "@types/react": "18.3.8", "@types/react-dom": "18.3.0", "eslint": "8.57.0", "eslint-config-next": "14.2.5", "tailwindcss": "3.4.10", "postcss": "8.4.47", "autoprefixer": "10.4.20", "prisma": "5.20.0", "ts-node": "10.9.2"}}