export type CompensationMode = 'PER_PARCEL' | 'PER_DELIVERY_NOTE' | 'MONTHLY'

type Rates = {
  ratePerParcel?: number
  perDeliveryNote?: number
  monthly?: number
}

export function computeCompensation(
  mode: CompensationMode,
  rates: Rates | null,
  opts: { collectedParcelsCount?: number; deliveryNoteComplete?: boolean }
) {
  const r = rates ?? {}
  switch (mode) {
    case 'PER_PARCEL': {
      const count = opts.collectedParcelsCount ?? 0
      const rate = r.ratePerParcel ?? 0
      return rate * count
    }
    case 'PER_DELIVERY_NOTE': {
      if (opts.deliveryNoteComplete) return r.perDeliveryNote ?? 0
      return 0
    }
    case 'MONTHLY': {
      return r.monthly ?? 0
    }
    default:
      return 0
  }
}
