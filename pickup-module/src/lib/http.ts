import { ZodSchema } from 'zod'

export async function parseJson<T>(req: Request, schema: ZodSchema<T>) {
  try {
    const json = await req.json()
    const data = schema.parse(json)
    return { ok: true as const, data }
  } catch {
    return { ok: false as const, error: 'Donn<PERSON> invalides.' }
  }
}

export function json(data: any, init?: ResponseInit) {
  return new Response(JSON.stringify(data), { status: 200, headers: { 'content-type': 'application/json' }, ...init })
}

