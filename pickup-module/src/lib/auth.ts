import { cookies } from 'next/headers'
import { prisma } from '@/lib/db'

export type Role = 'CUSTOMER' | 'PICKUP_AGENT' | 'ADMIN' | 'ACCOUNTANT'

export type Session = {
  userId: string
  role: Role
}

const COOKIE_NAME = 'demo_session'

export async function getSession(): Promise<Session | null> {
  const store = await cookies()
  const raw = store.get(COOKIE_NAME)?.value
  if (!raw) return null
  try {
    const data = JSON.parse(raw) as Session
    return data
  } catch {
    return null
  }
}

export async function requireSession(roles?: Role[]) {
  const session = await getSession()
  if (!session) return { ok: false as const, error: 'Accès refusé.' }
  if (roles && !roles.includes(session.role)) return { ok: false as const, error: 'Accès refusé.' }
  return { ok: true as const, session }
}

export async function setSession(session: Session) {
  const store = await cookies()
  store.set(COOKIE_NAME, JSON.stringify(session), { httpOnly: false, sameSite: 'lax' })
}

export async function clearSession() {
  const store = await cookies()
  store.delete(COOKIE_NAME)
}

export async function getCurrentUser() {
  const session = await getSession()
  if (!session) return null
  return prisma.user.findUnique({ where: { id: session.userId } })
}
