import { z } from 'zod'

export const CreatePickupRequestSchema = z.object({
  itemsCount: z.number().int().min(1, { message: 'Donn<PERSON> invalides.' }),
  preferredDate: z.string().datetime().optional(),
  slotStart: z.string().datetime().optional(),
  slotEnd: z.string().datetime().optional(),
})

export const AcceptSchema = z.object({})
export const RejectSchema = z.object({ reason: z.string().min(1, { message: 'Données invalides.' }) })
export const AssignSchema = z.object({ agentId: z.string().min(1, { message: 'Données invalides.' }) })
export const ScheduleSchema = z.object({
  slotStart: z.string().datetime({ message: 'Données invalides.' }),
  slotEnd: z.string().datetime({ message: 'Données invalides.' }),
})
export const CollectSchema = z.object({})

export const BulkReassignSchema = z.object({
  fromAgentId: z.string(),
  toAgentId: z.string(),
  scope: z.object({ customers: z.boolean().default(true), requests: z.boolean().default(true) }).default({ customers: true, requests: true })
})

export const LedgerQuerySchema = z.object({ agentId: z.string().optional(), status: z.string().optional() })

