"use client"
import { useEffect, useMemo, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'

export default function Toaster() {
  const search = useSearchParams()
  const router = useRouter()
  const msg = useMemo(() => search.get('toast'), [search])
  const [open, setOpen] = useState(!!msg)

  useEffect(() => {
    setOpen(!!msg)
    if (msg) {
      const t = setTimeout(() => setOpen(false), 3000)
      return () => clearTimeout(t)
    }
  }, [msg])

  useEffect(() => {
    if (!open && msg) {
      const params = new URLSearchParams(Array.from(search.entries()))
      params.delete('toast')
      router.replace(`?${params.toString()}`)
    }
  }, [open, msg, router, search])

  if (!msg || !open) return null

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="bg-white text-black rounded-xl shadow-soft px-4 py-2 animate-[toast-in_200ms_ease-out]">
        {msg}
      </div>
      <style jsx global>{`
        @keyframes toast-in { from { opacity: 0; transform: translateY(-6px) } to { opacity: 1; transform: translateY(0) } }
      `}</style>
    </div>
  )
}

