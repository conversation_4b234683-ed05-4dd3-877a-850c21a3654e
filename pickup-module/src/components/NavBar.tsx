import { getCurrentUser, clearSession } from "@/lib/auth";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function NavBar() {
  const user = await getCurrentUser();

  async function logout() {
    "use server";
    await clearSession();
    redirect("/");
  }

  return (
    <div className="flex items-center justify-between mb-6">
      <Link href="/" className="text-xl font-semibold">
        Deliverio - Ramassage
      </Link>
      {user ? (
        <form action={logout}>
          <button className="btn btn-primary">Se déconnecter</button>
        </form>
      ) : (
        <Link className="btn btn-primary" href="/">
          Connexion
        </Link>
      )}
    </div>
  );
}
