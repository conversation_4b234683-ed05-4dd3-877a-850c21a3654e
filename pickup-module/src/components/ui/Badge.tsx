export default function Badge({ kind = 'neutral', children }: { kind?: 'neutral' | 'success' | 'warning' | 'info' | 'danger'; children: React.ReactNode }) {
  const cls = {
    neutral: 'badge badge-neutral',
    success: 'badge badge-success',
    warning: 'badge badge-warning',
    info: 'badge badge-info',
    danger: 'badge badge-danger',
  }[kind]
  return <span className={cls}>{children}</span>
}

export function statusToKind(status?: string): 'neutral' | 'success' | 'warning' | 'info' | 'danger' {
  switch (status) {
    case 'REQUESTED':
      return 'info'
    case 'ACCEPTED':
      return 'info'
    case 'SCHEDULED':
      return 'warning'
    case 'IN_PICKUP':
      return 'warning'
    case 'COMPLETED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return 'neutral'
  }
}

