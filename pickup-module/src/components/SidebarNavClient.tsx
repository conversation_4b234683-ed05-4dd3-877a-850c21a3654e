"use client"
import Link from 'next/link'
import { usePathname, useSearchParams } from 'next/navigation'

export default function SidebarNavClient({ items }: { items: { href: string; label: string }[] }) {
  const pathname = usePathname()
  const hash = typeof window !== 'undefined' ? window.location.hash : ''

  return (
    <nav className="flex flex-col gap-1">
      {items.map((it) => {
        const active = pathname + hash === it.href || pathname === it.href.split('#')[0] && hash === `#${it.href.split('#')[1] ?? ''}`
        return (
          <Link key={it.href} href={it.href} className={active ? 'sidebar-link bg-slate-100' : 'sidebar-link'}>
            {it.label}
          </Link>
        )
      })}
    </nav>
  )
}

