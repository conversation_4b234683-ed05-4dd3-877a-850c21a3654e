export default function AgentCoverageManager({
  agents,
  cities,
  coverages,
  profiles,
  updateAgentCoverage,
}: {
  agents: { id: string; name: string }[]
  cities: { id: string; name: string; active: boolean }[]
  coverages: { agentId: string; cityId: string }[]
  profiles: { userId: string; enablePickup: boolean }[]
  updateAgentCoverage: (formData: FormData) => Promise<any>
}) {
  return (
    <div className="space-y-4">
      {agents.map((agent) => {
        const profile = profiles.find((p) => p.userId === agent.id)
        const covered = new Set(coverages.filter((cv) => cv.agentId === agent.id).map((cv) => cv.cityId))
        return (
          <form key={agent.id} action={updateAgentCoverage} className="border rounded-xl border-slate-200 p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="font-medium">{agent.name}</div>
              <label className="inline-flex items-center gap-2">
                <input type="checkbox" name="enablePickup" defaultChecked={!!profile?.enablePickup} />
                <span>Activer le ramassage</span>
              </label>
            </div>
            <input type="hidden" name="agentId" value={agent.id} />
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {cities.filter((c) => c.active).map((c) => (
                <label key={c.id} className="inline-flex items-center gap-2">
                  <input type="checkbox" name={`city_${c.id}`} defaultChecked={covered.has(c.id)} />
                  <span>{c.name}</span>
                </label>
              ))}
            </div>
            <div className="mt-3">
              <button className="btn btn-secondary">Enregistrer la couverture</button>
            </div>
          </form>
        )
      })}
    </div>
  )
}

