export default function RequestFilters({
  agents,
  cities,
  values,
}: {
  agents: { id: string; name: string }[]
  cities: { id: string; name: string }[]
  values: { status?: string; cityId?: string; agentId?: string; q?: string; sort?: string }
}) {
  return (
    <form method="GET" className="card p-4 flex flex-wrap gap-3 items-end">
      <div>
        <label className="block mb-1">Statut</label>
        <select name="status" defaultValue={values.status || ''} className="input input-sm">
          <option value="">Tous</option>
          <option>REQUESTED</option>
          <option>ACCEPTED</option>
          <option>SCHEDULED</option>
          <option>IN_PICKUP</option>
          <option>COMPLETED</option>
          <option>REJECTED</option>
        </select>
      </div>
      <div>
        <label className="block mb-1">Ville</label>
        <select name="cityId" defaultValue={values.cityId || ''} className="input input-sm">
          <option value="">Toutes</option>
          {cities.map((c) => (
            <option key={c.id} value={c.id}>
              {c.name}
            </option>
          ))}
        </select>
      </div>
      <div>
        <label className="block mb-1">Agent</label>
        <select name="agentId" defaultValue={values.agentId || ''} className="input input-sm">
          <option value="">Tous</option>
          {agents.map((a) => (
            <option key={a.id} value={a.id}>
              {a.name}
            </option>
          ))}
        </select>
      </div>
      <div className="min-w-[200px]">
        <label className="block mb-1">Recherche</label>
        <input name="q" defaultValue={values.q || ''} className="input input-sm" placeholder="ID ou Client" />
      </div>
      <div>
        <label className="block mb-1">Trier</label>
        <select name="sort" defaultValue={values.sort || 'new'} className="input input-sm">
          <option value="new">Plus récents</option>
          <option value="old">Plus anciens</option>
        </select>
      </div>
      <div className="flex gap-2 ml-auto">
        <button className="btn btn-secondary btn-sm" type="submit">
          Appliquer
        </button>
        <a className="btn btn-secondary btn-sm" href="/admin/requests">
          Réinitialiser
        </a>
      </div>
    </form>
  )
}

