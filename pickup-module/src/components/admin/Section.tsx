import type { ReactNode } from 'react'

export default function Section({ title, subtitle, children, id }: { title: string; subtitle?: string; children: ReactNode; id?: string }) {
  return (
    <section id={id} className="space-y-3">
      <div>
        <h3 className="text-xl font-semibold">{title}</h3>
        {subtitle && <p className="text-slate-600 text-sm">{subtitle}</p>}
      </div>
      <div className="card p-5">
        {children}
      </div>
    </section>
  )
}

