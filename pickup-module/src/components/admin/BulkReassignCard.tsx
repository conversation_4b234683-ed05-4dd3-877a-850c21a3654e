export default function BulkReassignCard({ agents, bulkReassign }: { agents: { id: string; name: string }[]; bulkReassign: (formData: FormData) => Promise<any> }) {
  return (
    <div className="space-y-3">
      <div className="font-semibold">Réaffectation en masse</div>
      <form action={bulkReassign} className="flex flex-wrap gap-2 items-end">
        <div>
          <label className="block mb-1">De</label>
          <select name="fromAgentId" className="input">
            {agents.map((a) => (
              <option key={a.id} value={a.id}>
                {a.name}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block mb-1">À</label>
          <select name="toAgentId" className="input">
            {agents.map((a) => (
              <option key={a.id} value={a.id}>
                {a.name}
              </option>
            ))}
          </select>
        </div>
        <button className="btn btn-primary">Réaffecter</button>
      </form>
    </div>
  )
}

