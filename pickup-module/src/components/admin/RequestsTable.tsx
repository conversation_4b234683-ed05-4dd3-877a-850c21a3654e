import { User } from '@prisma/client'
import Badge, { statusToKind } from '@/components/ui/Badge'

type Request = {
  id: string
  status: string
  customerId: string
  agentId: string | null
  slotStart: Date | null
  cityId: string | null
}

export default function RequestsTable({
  requests,
  agents,
  cities,
}: {
  requests: Request[]
  agents: User[]
  cities: { id: string; name: string }[]
}) {
  return (
    <div className="overflow-x-auto">
      <table className="table">
        <thead>
          <tr>
            <th className="py-2">ID</th>
            <th>Statut</th>
            <th>Ville</th>
            <th>Client</th>
            <th>Agent</th>
            <th>Créneau</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {requests.map((r) => (
            <tr key={r.id} className="border-t border-slate-200 align-top">
              <td className="py-2">{r.id.slice(0, 8)}</td>
              <td><Badge kind={statusToKind(r.status)}>{r.status}</Badge></td>
              <td>{cities.find((c) => c.id === r.cityId)?.name ?? '-'}</td>
              <td>{r.customerId.slice(0, 8)}</td>
              <td>{r.agentId ? r.agentId.slice(0, 8) : '-'}</td>
              <td>{r.slotStart ? new Date(r.slotStart).toLocaleString() : '-'}</td>
              <td>
                <a className="btn btn-secondary btn-sm" href={`/admin/requests/${r.id}`}>Gérer</a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
