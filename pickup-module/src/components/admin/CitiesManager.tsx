export default function CitiesManager({ cities, addCity, toggleCity }: {
  cities: { id: string; name: string; active: boolean }[]
  addCity: (formData: FormData) => Promise<any>
  toggleCity: (formData: FormData) => Promise<any>
}) {
  return (
    <div className="space-y-3">
      <form action={addCity} className="flex gap-2">
        <input className="input" name="name" placeholder="Nom de la ville" />
        <button className="btn btn-primary">Ajouter</button>
      </form>
      <div className="overflow-x-auto">
        <table className="w-full text-left">
          <thead className="text-slate-600">
            <tr>
              <th className="py-2">Nom</th>
              <th>Active</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {cities.map((c) => (
              <tr key={c.id} className="border-t border-slate-200">
                <td className="py-2">{c.name}</td>
                <td>{c.active ? 'Oui' : 'Non'}</td>
                <td>
                  <form action={toggleCity}>
                    <input type="hidden" name="id" value={c.id} />
                    <input type="hidden" name="active" value={(!c.active).toString()} />
                    <button className="btn btn-secondary">{c.active ? 'Désactiver' : 'Activer'}</button>
                  </form>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

