import Link from 'next/link'
import { getCurrentUser } from '@/lib/auth'
import SidebarNavClient from '@/components/SidebarNavClient'

function defaultPathForRole(role: string) {
  switch (role) {
    case 'CUSTOMER': return '/customer'
    case 'PICKUP_AGENT': return '/agent'
    case 'ADMIN': return '/admin'
    case 'ACCOUNTANT': return '/accountant'
    default: return '/'
  }
}

export default async function Sidebar() {
  const user = await getCurrentUser()
  if (!user) return null

  const items: { href: string; label: string }[] = []
  if (user.role === 'CUSTOMER') {
    items.push(
      { href: '/customer#create', label: 'Demander un ramassage' },
      { href: '/customer#list', label: 'Mes demandes' },
    )
  } else if (user.role === 'ADMIN') {
    items.push(
      { href: '/admin/requests', label: 'Demandes' },
      { href: '/admin/reassign', label: 'Réaffectation' },
      { href: '/admin/cities', label: 'Villes' },
      { href: '/admin/coverage', label: 'Couverture agents' },
      { href: '/accountant#ledger', label: 'Rémunération' },
    )
  } else if (user.role === 'PICKUP_AGENT') {
    items.push(
      { href: '/agent#pickups', label: 'Mes ramassages' },
    )
  } else if (user.role === 'ACCOUNTANT') {
    items.push(
      { href: '/accountant#ledger', label: 'Grand livre' },
    )
  }

  return (
    <aside className="card p-4 h-fit sticky top-6 space-y-3">
      <div className="text-slate-700 text-sm">{user.name}</div>
      <SidebarNavClient items={items} />
    </aside>
  )
}
