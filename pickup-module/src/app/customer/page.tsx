import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Sidebar from '@/components/Sidebar'

async function createPickupRequest(formData: FormData) {
  'use server'
  const { ok, session, error } = await requireSession(['CUSTOMER'])
  if (!ok || !session) return { error: error ?? 'Accès refusé.' }
  const itemsCount = Number(formData.get('itemsCount') || 1)
  const preferredDate = formData.get('preferredDate')?.toString()
  await prisma.pickupRequest.create({
    data: {
      customerId: session.userId,
      itemsCount,
      preferredDate: preferredDate ? new Date(preferredDate) : undefined,
      status: 'REQUESTED'
    } as any
  })
  redirect('/customer?toast=Demande%20envoy%C3%A9e.')
}

async function scheduleRequest(formData: FormData) {
  'use server'
  const { ok, session, error } = await requireSession(['CUSTOMER'])
  if (!ok || !session) return { error: error ?? 'Accès refusé.' }
  const id = String(formData.get('id'))
  const slotStart = new Date(String(formData.get('slotStart')))
  const slotEnd = new Date(String(formData.get('slotEnd')))

  // naive capacity check: cap 50 per slot globally in prototype
  const countSameSlot = await prisma.pickupRequest.count({ where: { slotStart, slotEnd, status: { in: ['SCHEDULED', 'IN_PICKUP'] } } })
  if (countSameSlot >= 50) return { error: 'Capacité atteinte pour ce créneau.' }

  await prisma.pickupRequest.update({ where: { id }, data: { slotStart, slotEnd, status: 'SCHEDULED' } as any })
  redirect('/customer?toast=Cr%C3%A9neau%20confirm%C3%A9.')
}

async function createStandingSchedule(formData: FormData) {
  'use server'
  const gate = await requireSession(['CUSTOMER'])
  if (!gate.ok) return { error: gate.error }
  const cityId = String(formData.get('cityId'))
  const timeSlot = String(formData.get('timeSlot'))
  await prisma.pickupSchedule.create({ data: { customerId: gate.session!.userId, cityId, timeSlot, active: true } })
  redirect('/customer?toast=Ramassage%20quotidien%20enregistr%C3%A9.')
}

async function createDeliveryNote(formData: FormData) {
  'use server'
  const gate = await requireSession(['CUSTOMER'])
  if (!gate.ok) return { error: gate.error }
  const scheduleId = String(formData.get('scheduleId'))
  const itemsCount = Number(formData.get('itemsCount') || 1)
  const schedule = await prisma.pickupSchedule.findUnique({ where: { id: scheduleId } })
  if (!schedule || !schedule.active) return { error: 'Données invalides.' }
  // Create DN
  const dn = await prisma.deliveryNote.create({ data: { customerId: gate.session!.userId, cityId: schedule.cityId, itemsCount, status: 'NEW' } })
  // Find agent with coverage for city
  const coverage = await prisma.pickupAgentCoverage.findFirst({ where: { cityId: schedule.cityId, active: true } })
  const now = new Date()
  const start = new Date(now)
  const end = new Date(now)
  if (schedule.timeSlot === 'AM') { start.setHours(9,0,0,0); end.setHours(12,0,0,0) } else { start.setHours(14,0,0,0); end.setHours(18,0,0,0) }
  await prisma.pickupRequest.create({
    data: {
      customerId: gate.session!.userId,
      status: 'SCHEDULED',
      slotStart: start,
      slotEnd: end,
      cityId: schedule.cityId,
      agentId: coverage?.agentId,
      itemsCount,
    }
  })
  redirect('/customer?toast=Bon%20de%20livraison%20et%20ramassage%20planifi%C3%A9s.')
}

export default async function CustomerPage() {
  const gate = await requireSession(['CUSTOMER'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>
  const session = gate.session!

  const [requests, schedules, cities] = await Promise.all([
    prisma.pickupRequest.findMany({ where: { customerId: session.userId }, orderBy: { createdAt: 'desc' } }),
    prisma.pickupSchedule.findMany({ where: { customerId: session.userId, active: true } }),
    prisma.city.findMany({ where: { active: true }, orderBy: { name: 'asc' } })
  ])

  // Available cities = cities with at least one enabled agent coverage
  const coverages = await prisma.pickupAgentCoverage.findMany({ where: { active: true } })
  const availableCityIds = new Set(coverages.map(c => c.cityId))
  const availableCities = cities.filter(c => availableCityIds.has(c.id))

  return (
    <main className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-[240px,1fr] gap-6">
        {/* Sidebar */}
        <Sidebar />

        {/* Content */}
        <div className="space-y-6">
          <h2 id="create" className="text-2xl font-bold">Demander un ramassage</h2>
          <form className="card p-6 flex gap-4 items-end" action={createPickupRequest}>
          <div>
            <label className="block mb-1">Quantité de colis</label>
            <input className="input" type="number" name="itemsCount" min={1} defaultValue={1} />
          </div>
          <div>
            <label className="block mb-1">Date préférée</label>
            <input className="input" type="datetime-local" name="preferredDate" />
          </div>
          <button className="btn btn-primary" type="submit">Envoyer la demande</button>
          </form>

          <h3 id="list" className="text-xl font-semibold">Mes demandes</h3>
          <div className="card p-4">
            <table className="w-full text-left">
              <thead className="text-slate-600">
                <tr>
                  <th className="py-2">ID</th>
                  <th>Statut</th>
                  <th>Ville</th>
                  <th>Colis</th>
                  <th>Créneau</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {requests.map((r) => (
                  <tr key={r.id} className="border-t border-slate-200">
                    <td className="py-2">{r.id.slice(0, 8)}</td>
                    <td>{r.status}</td>
                    <td>{cities.find(c => c.id === r.cityId)?.name ?? '-'}</td>
                    <td>{r.itemsCount}</td>
                    <td>{r.slotStart ? new Date(r.slotStart).toLocaleString() : '-'}</td>
                    <td>
                      {(r.status === 'REQUESTED' || r.status === 'ACCEPTED') && (
                        <form action={scheduleRequest} className="flex gap-2 items-end">
                          <input type="hidden" name="id" value={r.id} />
                          <input className="input" type="datetime-local" name="slotStart" required />
                          <input className="input" type="datetime-local" name="slotEnd" required />
                          <button className="btn btn-secondary" type="submit">Planifier</button>
                        </form>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <h3 className="text-xl font-semibold">Ramassage quotidien</h3>
          <div className="card p-4 space-y-3">
            <form action={createStandingSchedule} className="flex flex-wrap gap-2 items-end">
              <div>
                <label className="block mb-1">Ville</label>
                <select name="cityId" className="input">
                  {availableCities.map(c => (<option key={c.id} value={c.id}>{c.name}</option>))}
                </select>
              </div>
              <div>
                <label className="block mb-1">Créneau quotidien</label>
                <select name="timeSlot" className="input">
                  <option value="AM">Matin (09:00-12:00)</option>
                  <option value="PM">Après-midi (14:00-18:00)</option>
                </select>
              </div>
              <button className="btn btn-primary">Enregistrer</button>
            </form>
            {schedules.length > 0 && (
              <table className="w-full text-left">
                <thead className="text-slate-600">
                  <tr>
                    <th className="py-2">Ville</th>
                    <th>Créneau</th>
                  </tr>
                </thead>
                <tbody>
                  {schedules.map(s => {
                    const city = cities.find(c => c.id === s.cityId)
                    return (
                      <tr key={s.id} className="border-t border-slate-200">
                        <td className="py-2">{city?.name ?? s.cityId}</td>
                        <td>{s.timeSlot === 'AM' ? 'Matin' : 'Après-midi'}</td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            )}
          </div>

          <h3 className="text-xl font-semibold">Créer un bon de livraison</h3>
          <div className="card p-4">
            {schedules.length === 0 ? (
              <div className="text-slate-600">Configurez d'abord un ramassage quotidien.</div>
            ) : (
              <form action={createDeliveryNote} className="flex gap-2 items-end">
                <div>
                  <label className="block mb-1">Planning</label>
                  <select name="scheduleId" className="input">
                    {schedules.map(s => {
                      const city = cities.find(c => c.id === s.cityId)
                      return (<option key={s.id} value={s.id}>{city?.name} — {s.timeSlot === 'AM' ? 'Matin' : 'Après-midi'}</option>)
                    })}
                  </select>
                </div>
                <div>
                  <label className="block mb-1">Colis</label>
                  <input className="input" type="number" name="itemsCount" min={1} defaultValue={1} />
                </div>
                <button className="btn btn-primary">Créer</button>
              </form>
            )}
          </div>
        </div>
      </div>
    </main>
  )
}
