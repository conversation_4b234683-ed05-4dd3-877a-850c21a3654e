import './globals.css'
import type { ReactNode } from 'react'
import NavBar from '@/components/NavBar'
import Toaster from '@/components/Toaster'

export const metadata = {
  title: 'Deliverio Pickup Module',
  description: 'Prototype — Ramassage',
}

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="fr">
      <body>
        <div className="min-h-screen p-6">
          <div className="max-w-6xl mx-auto">
            <NavBar />
            <Toaster />
            {children}
          </div>
        </div>
      </body>
    </html>
  )
}
