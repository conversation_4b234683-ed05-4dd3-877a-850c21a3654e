import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Section from '@/components/admin/Section'
import RequestsTable from '@/components/admin/RequestsTable'
import RequestFilters from '@/components/admin/RequestFilters'

async function accept(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  await prisma.pickupRequest.update({ where: { id }, data: { status: 'ACCEPTED', rejectionReason: null } as any })
  redirect('/admin/requests?toast=Demande%20accept%C3%A9e.')
}

async function reject(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const reason = String(formData.get('reason') || '')
  await prisma.pickupRequest.update({ where: { id }, data: { status: 'REJECTED', rejectionReason: reason } as any })
  redirect('/admin/requests?toast=Demande%20refus%C3%A9e.')
}

async function assign(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const agentId = String(formData.get('agentId'))

  const agentProfile = await prisma.pickupAgentProfile.findUnique({ where: { userId: agentId } })
  if (!agentProfile || !agentProfile.enablePickup || agentProfile.suspended) return { error: 'Accès refusé.' }

  const reqDoc = await prisma.pickupRequest.findUnique({ where: { id } })
  if (!reqDoc) return { error: 'Données invalides.' }
  if (agentProfile.enabledCustomerIds) {
    const ids = JSON.parse(agentProfile.enabledCustomerIds) as string[]
    const allowed = Array.isArray(ids) && ids.includes(reqDoc.customerId)
    if (!allowed) return { error: 'Accès refusé.' }
  }

  if (reqDoc.cityId) {
    const cov = await prisma.pickupAgentCoverage.findFirst({ where: { agentId, cityId: reqDoc.cityId, active: true } })
    if (!cov) return { error: 'Accès refusé.' }
  }
  await prisma.pickupRequest.update({ where: { id }, data: { agentId, status: 'IN_PICKUP' } as any })
  redirect('/admin/requests?toast=Agent%20attribu%C3%A9.')
}

async function schedule(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const slotStart = new Date(String(formData.get('slotStart')))
  const slotEnd = new Date(String(formData.get('slotEnd')))
  await prisma.pickupRequest.update({ where: { id }, data: { slotStart, slotEnd, status: 'SCHEDULED' } as any })
  redirect('/admin/requests?toast=Cr%C3%A9neau%20planifi%C3%A9.')
}

async function scheduleQuick(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const when = String(formData.get('when')) as 'AM' | 'PM'
  const now = new Date()
  const start = new Date(now)
  const end = new Date(now)
  if (when === 'AM') {
    start.setHours(9, 0, 0, 0)
    end.setHours(12, 0, 0, 0)
  } else {
    start.setHours(14, 0, 0, 0)
    end.setHours(18, 0, 0, 0)
  }
  await prisma.pickupRequest.update({ where: { id }, data: { slotStart: start, slotEnd: end, status: 'SCHEDULED' } as any })
  redirect('/admin/requests?toast=Cr%C3%A9neau%20planifi%C3%A9')
}

async function assignAuto(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const req = await prisma.pickupRequest.findUnique({ where: { id } })
  if (!req?.cityId) return { error: 'Données invalides.' }
  const cov = await prisma.pickupAgentCoverage.findFirst({ where: { cityId: req.cityId, active: true } })
  if (!cov) return { error: 'Accès refusé.' }
  await prisma.pickupRequest.update({ where: { id }, data: { agentId: cov.agentId, status: 'IN_PICKUP' } as any })
  redirect('/admin/requests?toast=Agent%20attribu%C3%A9')
}

export default async function AdminRequestsPage({ searchParams }: { searchParams: Record<string, string | string[] | undefined> }) {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>

  const status = (searchParams.status as string) || undefined
  const cityId = (searchParams.cityId as string) || undefined
  const agentId = (searchParams.agentId as string) || undefined
  const q = (searchParams.q as string) || undefined
  const sort = (searchParams.sort as string) || 'new'

  const where: any = {}
  if (status) where.status = status
  if (cityId) where.cityId = cityId
  if (agentId) where.agentId = agentId
  if (q) {
    where.OR = [
      { id: { contains: q } },
      { customerId: { contains: q } },
    ]
  }

  const [requests, agents, cities] = await Promise.all([
    prisma.pickupRequest.findMany({ where, orderBy: { createdAt: sort === 'old' ? 'asc' : 'desc' } }),
    prisma.user.findMany({ where: { role: 'PICKUP_AGENT' }, orderBy: { name: 'asc' } }),
    prisma.city.findMany({ orderBy: { name: 'asc' } }),
  ])

  return (
    <>
      <Section title="Demandes" subtitle="Filtrez, attribuez et planifiez en toute simplicité.">
        <RequestFilters agents={agents} cities={cities} values={{ status, cityId, agentId, q, sort }} />
      </Section>
      <Section title="Liste" subtitle="Ouvrez les fiches pour gérer les demandes.">
        <RequestsTable
          requests={requests as any}
          agents={agents}
          cities={cities}
        />
      </Section>
    </>
  )
}
