import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Section from '@/components/admin/Section'
import Badge, { statusToKind } from '@/components/ui/Badge'

async function accept(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  await prisma.pickupRequest.update({ where: { id }, data: { status: 'ACCEPTED', rejectionReason: null } as any })
  redirect(`/admin/requests/${id}?toast=Demande%20accept%C3%A9e`)
}

async function reject(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const reason = String(formData.get('reason') || '')
  await prisma.pickupRequest.update({ where: { id }, data: { status: 'REJECTED', rejectionReason: reason } as any })
  redirect(`/admin/requests/${id}?toast=Demande%20refus%C3%A9e`)
}

async function assign(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const agentId = String(formData.get('agentId'))
  const agentProfile = await prisma.pickupAgentProfile.findUnique({ where: { userId: agentId } })
  if (!agentProfile || !agentProfile.enablePickup || agentProfile.suspended) return { error: 'Accès refusé.' }
  const reqDoc = await prisma.pickupRequest.findUnique({ where: { id } })
  if (!reqDoc) return { error: 'Données invalides.' }
  if (agentProfile.enabledCustomerIds) {
    const ids = JSON.parse(agentProfile.enabledCustomerIds) as string[]
    const allowed = Array.isArray(ids) && ids.includes(reqDoc.customerId)
    if (!allowed) return { error: 'Accès refusé.' }
  }
  if (reqDoc.cityId) {
    const cov = await prisma.pickupAgentCoverage.findFirst({ where: { agentId, cityId: reqDoc.cityId, active: true } })
    if (!cov) return { error: 'Accès refusé.' }
  }
  await prisma.pickupRequest.update({ where: { id }, data: { agentId, status: 'IN_PICKUP' } as any })
  redirect(`/admin/requests/${id}?toast=Agent%20attribu%C3%A9`)
}

async function schedule(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const slotStart = new Date(String(formData.get('slotStart')))
  const slotEnd = new Date(String(formData.get('slotEnd')))
  await prisma.pickupRequest.update({ where: { id }, data: { slotStart, slotEnd, status: 'SCHEDULED' } as any })
  redirect(`/admin/requests/${id}?toast=Cr%C3%A9neau%20planifi%C3%A9`)
}

async function scheduleQuick(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const when = String(formData.get('when')) as 'AM' | 'PM'
  const now = new Date()
  const start = new Date(now)
  const end = new Date(now)
  if (when === 'AM') { start.setHours(9,0,0,0); end.setHours(12,0,0,0) } else { start.setHours(14,0,0,0); end.setHours(18,0,0,0) }
  await prisma.pickupRequest.update({ where: { id }, data: { slotStart: start, slotEnd: end, status: 'SCHEDULED' } as any })
  redirect(`/admin/requests/${id}?toast=Cr%C3%A9neau%20planifi%C3%A9`)
}

export default async function AdminRequestDetail({ params }: { params: { id: string } }) {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>
  const id = params.id
  const req = await prisma.pickupRequest.findUnique({ where: { id } })
  if (!req) return <div className="text-red-300">Demande introuvable.</div>
  const [customer, agent, city, agents] = await Promise.all([
    prisma.user.findUnique({ where: { id: req.customerId } }),
    req.agentId ? prisma.user.findUnique({ where: { id: req.agentId } }) : Promise.resolve(null),
    req.cityId ? prisma.city.findUnique({ where: { id: req.cityId } }) : Promise.resolve(null),
    prisma.user.findMany({ where: { role: 'PICKUP_AGENT' }, orderBy: { name: 'asc' } }),
  ])

  return (
    <div className="space-y-6">
      <Section title={`Demande ${req.id.slice(0,8)}`} subtitle="Détails et actions">
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div><span className="text-slate-600">Statut:</span> <Badge kind={statusToKind(req.status)}>{req.status}</Badge></div>
            <div><span className="text-slate-600">Client:</span> {customer?.name ?? req.customerId}</div>
            <div><span className="text-slate-600">Ville:</span> {city?.name ?? '-'}</div>
            <div><span className="text-slate-600">Agent:</span> {agent?.name ?? '-'}</div>
          </div>
          <div className="space-y-2">
            <div><span className="text-slate-600">Créneau:</span> {req.slotStart ? new Date(req.slotStart).toLocaleString() : '-'}</div>
            <div><span className="text-slate-600">Colis:</span> {req.itemsCount}</div>
            {req.rejectionReason && <div className="text-rose-600">Motif: {req.rejectionReason}</div>}
          </div>
        </div>
      </Section>

      {req.status === 'REQUESTED' && (
        <Section title="Décision" subtitle="Accepter ou refuser la demande.">
          <div className="flex flex-wrap gap-2 items-end">
            <form action={accept}>
              <input type="hidden" name="id" value={id} />
              <button className="btn btn-primary">Accepter</button>
            </form>
            <form action={reject} className="flex gap-2 items-end">
              <input type="hidden" name="id" value={id} />
              <input name="reason" className="input" placeholder="Motif" />
              <button className="btn btn-secondary">Refuser</button>
            </form>
          </div>
        </Section>
      )}

      {req.status !== 'REJECTED' && req.status !== 'COMPLETED' && (
        <Section title="Attribution et planification" subtitle="Définissez l'agent et le créneau.">
          <div className="grid md:grid-cols-2 gap-4">
            <form action={assign} className="space-y-2">
              <input type="hidden" name="id" value={id} />
              <div className="text-sm text-slate-600">Attribuer un agent</div>
              <select name="agentId" className="input">
                {agents.map(a => (<option key={a.id} value={a.id}>{a.name}</option>))}
              </select>
              <button className="btn btn-secondary">Attribuer</button>
            </form>
            <form action={schedule} className="space-y-2">
              <input type="hidden" name="id" value={id} />
              <div className="text-sm text-slate-600">Planifier un créneau précis</div>
              <div className="flex gap-2">
                <input className="input" type="datetime-local" name="slotStart" required />
                <input className="input" type="datetime-local" name="slotEnd" required />
              </div>
              <button className="btn btn-secondary">Planifier</button>
            </form>
          </div>
          <div className="flex gap-2">
            <form action={scheduleQuick}>
              <input type="hidden" name="id" value={id} />
              <input type="hidden" name="when" value="AM" />
              <button className="btn btn-secondary">Planifier AM</button>
            </form>
            <form action={scheduleQuick}>
              <input type="hidden" name="id" value={id} />
              <input type="hidden" name="when" value="PM" />
              <button className="btn btn-secondary">Planifier PM</button>
            </form>
          </div>
        </Section>
      )}
    </div>
  )
}
