import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Section from '@/components/admin/Section'
import CitiesManager from '@/components/admin/CitiesManager'

async function addCity(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const name = String(formData.get('name') || '')
  if (!name.trim()) return { error: 'Données invalides.' }
  await prisma.city.upsert({ where: { name }, update: { active: true }, create: { name, active: true } })
  redirect('/admin/cities?toast=Ville%20ajout%C3%A9e')
}

async function toggleCity(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const active = String(formData.get('active')) === 'true'
  await prisma.city.update({ where: { id }, data: { active } })
  redirect('/admin/cities?toast=Ville%20mise%20%C3%A0%20jour')
}

export default async function AdminCitiesPage() {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>
  const cities = await prisma.city.findMany({ orderBy: { name: 'asc' } })
  return (
    <Section title="Villes" subtitle="Activez les villes et gérez leur disponibilité.">
      <CitiesManager cities={cities} addCity={addCity} toggleCity={toggleCity} />
    </Section>
  )
}

