import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Section from '@/components/admin/Section'
import BulkReassignCard from '@/components/admin/BulkReassignCard'

async function bulkReassign(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const fromAgentId = String(formData.get('fromAgentId'))
  const toAgentId = String(formData.get('toAgentId'))
  await prisma.$transaction(async (tx) => {
    await tx.pickupAssignment.updateMany({ where: { agentId: fromAgentId, active: true }, data: { agentId: toAgentId } })
    await tx.pickupRequest.updateMany({ where: { agentId: fromAgentId, status: { in: ['REQUESTED','ACCEPTED','SCHEDULED','IN_PICKUP'] } }, data: { agentId: toAgentId } })
  })
  redirect('/admin/reassign?toast=R%C3%A9affectation%20effectu%C3%A9e.')
}

export default async function AdminReassignPage() {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>
  const agents = await prisma.user.findMany({ where: { role: 'PICKUP_AGENT' }, orderBy: { name: 'asc' } })
  return (
    <Section title="Réaffectation" subtitle="Déplacez rapidement les demandes d'un agent à un autre.">
      <BulkReassignCard agents={agents} bulkReassign={bulkReassign} />
    </Section>
  )
}

