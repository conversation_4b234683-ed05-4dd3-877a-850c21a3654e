import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Section from '@/components/admin/Section'
import AgentCoverageManager from '@/components/admin/AgentCoverageManager'

async function updateAgentCoverage(formData: FormData) {
  'use server'
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const agentId = String(formData.get('agentId'))
  const enablePickup = String(formData.get('enablePickup') || '') === 'on'
  const cityIds = new Set<string>()
  for (const [k, v] of formData.entries()) {
    if (k.startsWith('city_') && v === 'on') cityIds.add(k.replace('city_', ''))
  }
  await prisma.$transaction(async (tx) => {
    await tx.pickupAgentProfile.update({ where: { userId: agentId }, data: { enablePickup } })
    await tx.pickupAgentCoverage.deleteMany({ where: { agentId } })
    if (cityIds.size > 0) {
      await tx.pickupAgentCoverage.createMany({ data: Array.from(cityIds).map((id) => ({ agentId, cityId: id, active: true })) })
    }
  })
  redirect('/admin/coverage?toast=Couverture%20mise%20%C3%A0%20jour')
}

export default async function AdminCoveragePage() {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>
  const [agents, cities, coverages, profiles] = await Promise.all([
    prisma.user.findMany({ where: { role: 'PICKUP_AGENT' }, orderBy: { name: 'asc' } }),
    prisma.city.findMany({ orderBy: { name: 'asc' } }),
    prisma.pickupAgentCoverage.findMany({ where: { active: true } }),
    prisma.pickupAgentProfile.findMany({})
  ])
  return (
    <Section title="Couverture des agents" subtitle="Sélectionnez les villes couvertes par chaque agent.">
      <AgentCoverageManager
        agents={agents}
        cities={cities}
        coverages={coverages}
        profiles={profiles.map(p => ({ userId: p.userId, enablePickup: p.enablePickup }))}
        updateAgentCoverage={updateAgentCoverage}
      />
    </Section>
  )
}

