import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { parseJson, json } from '@/lib/http'
import { CreatePickupRequestSchema } from '@/lib/schemas'

export async function POST(req: Request) {
  const gate = await requireSession(['CUSTOMER'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const body = await parseJson(req, CreatePickupRequestSchema)
  if (!body.ok) return json({ error: body.error }, { status: 400 })
  const { itemsCount, preferredDate, slotStart, slotEnd } = body.data
  const created = await prisma.pickupRequest.create({
    data: {
      customerId: gate.session!.userId,
      itemsCount,
      preferredDate: preferredDate ? new Date(preferredDate) : undefined,
      slotStart: slotStart ? new Date(slotStart) : undefined,
      slotEnd: slotEnd ? new Date(slotEnd) : undefined,
      status: 'REQUESTED'
    } as any
  })
  return json(created)
}

