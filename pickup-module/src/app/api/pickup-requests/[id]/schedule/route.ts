import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { parseJson, json } from '@/lib/http'
import { ScheduleSchema } from '@/lib/schemas'

export async function POST(req: Request, { params }: { params: { id: string } }) {
  const gate = await requireSession(['ADMIN','CUSTOMER'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const body = await parseJson(req, ScheduleSchema)
  if (!body.ok) return json({ error: body.error }, { status: 400 })
  const { slotStart, slotEnd } = body.data
  // naive capacity check (global 50 per slot)
  const countSameSlot = await prisma.pickupRequest.count({ where: { slotStart: new Date(slotStart), slotEnd: new Date(slotEnd), status: { in: ['SCHEDULED', 'IN_PICKUP'] } } })
  if (countSameSlot >= 50) return json({ error: 'Capacité atteinte pour ce créneau.' }, { status: 400 })
  const updated = await prisma.pickupRequest.update({ where: { id: params.id }, data: { slotStart: new Date(slotStart), slotEnd: new Date(slotEnd), status: 'SCHEDULED' } as any })
  return json(updated)
}

