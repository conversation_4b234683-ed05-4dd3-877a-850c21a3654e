import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { parseJson, json } from '@/lib/http'
import { AssignSchema } from '@/lib/schemas'

export async function POST(req: Request, { params }: { params: { id: string } }) {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const body = await parseJson(req, AssignSchema)
  if (!body.ok) return json({ error: body.error }, { status: 400 })
  const agentId = body.data.agentId
  const agentProfile = await prisma.pickupAgentProfile.findUnique({ where: { userId: agentId } })
  if (!agentProfile || !agentProfile.enablePickup || agentProfile.suspended) return json({ error: 'Accès refusé.' }, { status: 400 })
  const reqDoc = await prisma.pickupRequest.findUnique({ where: { id: params.id } })
  if (!reqDoc) return json({ error: 'Données invalides.' }, { status: 404 })
  // Validate city coverage if cityId present
  if (reqDoc.cityId) {
    const cov = await prisma.pickupAgentCoverage.findFirst({ where: { agentId, cityId: reqDoc.cityId, active: true } })
    if (!cov) return json({ error: 'Accès refusé.' }, { status: 400 })
  }
  if (agentProfile.enabledCustomerIds) {
    const ids = JSON.parse(agentProfile.enabledCustomerIds) as string[]
    const allowed = Array.isArray(ids) && ids.includes(reqDoc.customerId)
    if (!allowed) return json({ error: 'Accès refusé.' }, { status: 403 })
  }
  const updated = await prisma.pickupRequest.update({ where: { id: params.id }, data: { agentId, status: 'IN_PICKUP' } as any })
  return json(updated)
}
