import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { json } from '@/lib/http'
import { computeCompensation } from '@/lib/compensation'

export async function POST(_: Request, { params }: { params: { id: string } }) {
  const gate = await requireSession(['PICKUP_AGENT'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const reqDoc = await prisma.pickupRequest.findUnique({ where: { id: params.id } })
  if (!reqDoc) return json({ error: 'Données invalides.' }, { status: 404 })
  if (reqDoc.agentId !== gate.session!.userId) return json({ error: 'Accès refusé.' }, { status: 403 })
  if (reqDoc.slotStart && new Date(reqDoc.slotStart) > new Date()) return json({ error: 'Statut invalide. Choisissez une transition valide.' }, { status: 400 })

  const result = await prisma.$transaction(async (tx) => {
    const updated = await tx.pickupRequest.update({ where: { id: params.id }, data: { status: 'COMPLETED' } as any })
    const profile = await tx.pickupAgentProfile.findUnique({ where: { userId: gate.session!.userId } })
    if (profile) {
      const rates = profile.rates ? JSON.parse(profile.rates) as any : null
      const amount = computeCompensation(profile.compensationMode as any, rates, {
        collectedParcelsCount: reqDoc.itemsCount ?? 1,
        deliveryNoteComplete: true
      })
      if (amount > 0) {
        await tx.pickupCompensationLedger.create({ data: { agentId: gate.session!.userId, source: profile.compensationMode === 'PER_PARCEL' ? 'parcel' : (profile.compensationMode === 'PER_DELIVERY_NOTE' ? 'delivery_note' : 'monthly'), sourceId: reqDoc.id, amount, status: 'ready_to_invoice' } as any })
      }
    }
    return updated
  })
  return json(result)
}
