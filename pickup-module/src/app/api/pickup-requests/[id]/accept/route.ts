import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { json } from '@/lib/http'

export async function POST(_: Request, { params }: { params: { id: string } }) {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const updated = await prisma.pickupRequest.update({ where: { id: params.id }, data: { status: 'ACCEPTED', rejectionReason: null } as any })
  return json(updated)
}

