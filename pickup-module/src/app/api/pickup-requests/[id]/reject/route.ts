import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { parseJson, json } from '@/lib/http'
import { RejectSchema } from '@/lib/schemas'

export async function POST(req: Request, { params }: { params: { id: string } }) {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const body = await parseJson(req, RejectSchema)
  if (!body.ok) return json({ error: body.error }, { status: 400 })
  const updated = await prisma.pickupRequest.update({ where: { id: params.id }, data: { status: 'REJECTED', rejectionReason: body.data.reason } as any })
  return json(updated)
}

