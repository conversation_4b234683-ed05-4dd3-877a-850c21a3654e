import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { parseJson, json } from '@/lib/http'
import { BulkReassignSchema } from '@/lib/schemas'

export async function POST(req: Request) {
  const gate = await requireSession(['ADMIN'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const body = await parseJson(req, BulkReassignSchema)
  if (!body.ok) return json({ error: body.error }, { status: 400 })
  const { fromAgentId, toAgentId, scope } = body.data
  const result = await prisma.$transaction(async (tx) => {
    const changes: Record<string, number> = { assignments: 0, requests: 0 }
    if (scope.customers) {
      const updated = await tx.pickupAssignment.updateMany({ where: { agentId: fromAgentId, active: true }, data: { agentId: toAgentId } })
      changes.assignments = updated.count
    }
    if (scope.requests) {
      const updated = await tx.pickupRequest.updateMany({
        where: { agentId: fromAgentId, status: { in: ['REQUESTED','ACCEPTED','SCHEDULED','IN_PICKUP'] } },
        data: { agentId: toAgentId }
      })
      changes.requests = updated.count
    }
    return changes
  })
  return json({ ok: true, result })
}

