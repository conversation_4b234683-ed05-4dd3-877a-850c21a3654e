import { requireSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { json } from '@/lib/http'

export async function GET(req: Request) {
  const gate = await requireSession(['ACCOUNTANT','ADMIN'])
  if (!gate.ok) return json({ error: gate.error }, { status: 403 })
  const { searchParams } = new URL(req.url)
  const agentId = searchParams.get('agentId') || undefined
  const status = searchParams.get('status') || undefined
  const where: any = {}
  if (agentId) where.agentId = agentId
  if (status) where.status = status
  const entries = await prisma.pickupCompensationLedger.findMany({ where, orderBy: { createdAt: 'desc' } })
  return json(entries)
}

