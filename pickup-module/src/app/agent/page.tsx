import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { computeCompensation } from '@/lib/compensation'
import { redirect } from 'next/navigation'
import Sidebar from '@/components/Sidebar'

async function collect(formData: FormData) {
  'use server'
  const gate = await requireSession(['PICKUP_AGENT'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const req = await prisma.pickupRequest.findUnique({ where: { id } })
  if (!req) return { error: 'Données invalides.' }
  if (req.agentId !== gate.session!.userId) return { error: 'Accès refusé.' }
  if (req.slotStart && new Date(req.slotStart) > new Date()) return { error: 'Statut invalide. Choisissez une transition valide.' }

  await prisma.$transaction(async (tx) => {
    await tx.pickupRequest.update({ where: { id }, data: { status: 'COMPLETED' } as any })
    // Side effects: add compensation entry according to agent mode
    const profile = await tx.pickupAgentProfile.findUnique({ where: { userId: gate.session!.userId } })
    if (profile) {
      const rates = profile.rates ? JSON.parse(profile.rates) as any : null
      const amount = computeCompensation(profile.compensationMode as any, rates, {
        collectedParcelsCount: req.itemsCount ?? 1,
        deliveryNoteComplete: true
      })
      if (amount > 0) {
        await tx.pickupCompensationLedger.create({
          data: {
            agentId: gate.session!.userId,
            source: profile.compensationMode === 'PER_PARCEL' ? 'parcel' : (profile.compensationMode === 'PER_DELIVERY_NOTE' ? 'delivery_note' : 'monthly'),
            sourceId: req.id,
            amount,
            status: 'ready_to_invoice'
          } as any
        })
      }
    }
  })
  redirect('/agent?toast=Ramassage%20effectu%C3%A9')
}

export default async function AgentPage() {
  const gate = await requireSession(['PICKUP_AGENT'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>
  const agentId = gate.session!.userId
  const [requests, profile] = await Promise.all([
    prisma.pickupRequest.findMany({ where: { agentId, status: { in: ['SCHEDULED', 'IN_PICKUP'] } }, orderBy: { slotStart: 'asc' } }),
    prisma.pickupAgentProfile.findUnique({ where: { userId: agentId } })
  ])

  const groups: Record<string, typeof requests> = {}
  for (const r of requests) {
    let key = 'Sans créneau'
    if (r.slotStart) {
      const h = new Date(r.slotStart).getHours()
      key = h < 12 ? 'Matin' : 'Après-midi'
    }
    groups[key] = groups[key] || []
    groups[key].push(r)
  }

  const capacity = profile?.capacity ? JSON.parse(profile.capacity) as { AM?: number; PM?: number } : {}

  return (
    <main className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-[240px,1fr] gap-6">
        {/* Sidebar */}
        <Sidebar />

        {/* Content */}
        <div className="space-y-6">
      <h2 id="pickups" className="text-2xl font-bold">Mes ramassages</h2>
      <div className="card p-4">
        <table className="w-full text-left">
          <thead className="text-slate-600">
            <tr>
              <th className="py-2">ID</th>
              <th>Ville</th>
              <th>Créneau</th>
              <th>Colis</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {requests.map((r) => {
              let slot = 'Sans créneau'
              if (r.slotStart) {
                const h = new Date(r.slotStart).getHours()
                slot = h < 12 ? 'Matin' : 'Après-midi'
              }
              return (
                <tr key={r.id} className="border-t border-slate-200">
                  <td className="py-2">{r.id.slice(0,8)}</td>
                  <td>{/* city will be looked up client side? kept '-' for prototype */}{r.cityId ? r.cityId.slice(0,8) : '-'}</td>
                  <td>{slot}</td>
                  <td>{r.itemsCount}</td>
                  <td>
                    <form action={collect}>
                      <input type="hidden" name="id" value={r.id} />
                      <button className="btn btn-primary">Ramassage effectué</button>
                    </form>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
      {requests.length === 0 && <div className="text-slate-600">Aucune demande assignée.</div>}
        </div>
      </div>
    </main>
  )
}
