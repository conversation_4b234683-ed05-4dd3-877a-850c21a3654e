import Link from 'next/link'
import { prisma } from '@/lib/db'
import { setSession } from '@/lib/auth'
import { redirect } from 'next/navigation'

async function loginAs(userEmail: string) {
  'use server'
  const user = await prisma.user.findUnique({ where: { email: userEmail } })
  if (!user) return { ok: false }
  await setSession({ userId: user.id, role: user.role })
  return { ok: true, role: user.role }
}

async function loginWithEmail(formData: FormData) {
  'use server'
  const email = String(formData.get('email') || '')
  const to = String(formData.get('redirectTo') || '/')
  const res = await loginAs(email)
  if (res.ok) redirect(to)
  return res
}

function defaultPathForRole(role: string) {
  switch (role) {
    case 'CUSTOMER': return '/customer'
    case 'PICKUP_AGENT': return '/agent'
    case 'ADMIN': return '/admin'
    case 'ACCOUNTANT': return '/accountant'
    default: return '/'
  }
}

async function loginWithCredentials(formData: FormData) {
  'use server'
  const email = String(formData.get('email') || '')
  const _password = String(formData.get('password') || '')
  const user = await prisma.user.findUnique({ where: { email } })
  if (!user) {
    redirect('/?toast=Compte%20introuvable')
  }
  await setSession({ userId: user!.id, role: user!.role })
  redirect(defaultPathForRole(user!.role))
}

export default async function Home() {
  const users = await prisma.user.findMany({ orderBy: { createdAt: 'asc' } })
  const customer = users.find(u => u.role === 'CUSTOMER')
  const agent = users.find(u => u.role === 'PICKUP_AGENT')
  const admin = users.find(u => u.role === 'ADMIN')
  const accountant = users.find(u => u.role === 'ACCOUNTANT')

  return (
    <main>
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold">Page de démonstration</h1>
        <p className="text-slate-600">Connectez-vous ou choisissez un type de compte pour une session rapide.</p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
        <form className="card p-6 md:col-span-2 space-y-4" action={loginWithCredentials}>
          <div className="text-lg font-semibold">Connexion</div>
          <div>
            <label className="block mb-1">Email</label>
            <input name="email" type="email" className="input" placeholder="<EMAIL>" required />
          </div>
          <div>
            <label className="block mb-1">Mot de passe</label>
            <input name="password" type="password" className="input" placeholder="••••••••" required />
          </div>
          <button className="btn btn-primary w-full">Se connecter</button>
          <div className="text-xs text-slate-500">Astuce: utilisez un des emails de démonstration (<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>). Le mot de passe est ignoré.</div>
        </form>
        <form className="card p-6" action={loginWithEmail}>
          <div className="text-lg font-semibold mb-2">Client</div>
          <input type="hidden" name="email" value={customer?.email || ''} />
          <input type="hidden" name="redirectTo" value="/customer" />
          <button className="btn btn-primary w-full">Démarrer</button>
        </form>
        <form className="card p-6" action={loginWithEmail}>
          <div className="text-lg font-semibold mb-2">Agent de ramassage</div>
          <input type="hidden" name="email" value={agent?.email || ''} />
          <input type="hidden" name="redirectTo" value="/agent" />
          <button className="btn btn-primary w-full">Démarrer</button>
        </form>
        <form className="card p-6" action={loginWithEmail}>
          <div className="text-lg font-semibold mb-2">Administrateur</div>
          <input type="hidden" name="email" value={admin?.email || ''} />
          <input type="hidden" name="redirectTo" value="/admin" />
          <button className="btn btn-primary w-full">Démarrer</button>
        </form>
        <form className="card p-6" action={loginWithEmail}>
          <div className="text-lg font-semibold mb-2">Comptable</div>
          <input type="hidden" name="email" value={accountant?.email || ''} />
          <input type="hidden" name="redirectTo" value="/accountant" />
          <button className="btn btn-primary w-full">Démarrer</button>
        </form>
      </div>
      <div className="mt-6 text-slate-600">Astuce: les écrans sont en FR, le code en EN.</div>
    </main>
  )
}
