import { prisma } from '@/lib/db'
import { requireSession } from '@/lib/auth'
import { redirect } from 'next/navigation'
import Sidebar from '@/components/Sidebar'

async function mark(formData: FormData) {
  'use server'
  const gate = await requireSession(['ACCOUNTANT','ADMIN'])
  if (!gate.ok) return { error: gate.error }
  const id = String(formData.get('id'))
  const status = String(formData.get('status'))
  await prisma.pickupCompensationLedger.update({ where: { id }, data: { status } })
  redirect('/accountant?toast=Statut%20mis%20%C3%A0%20jour')
}

export default async function AccountantPage() {
  const gate = await requireSession(['ACCOUNTANT','ADMIN'])
  if (!gate.ok) return <div className="text-red-300">{gate.error}</div>

  const agents = await prisma.user.findMany({ where: { role: 'PICKUP_AGENT' } })
  const entries = await prisma.pickupCompensationLedger.findMany({ orderBy: { createdAt: 'desc' } })

  return (
    <main className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-[240px,1fr] gap-6">
        {/* Sidebar */}
        <Sidebar />

        {/* Content */}
        <div className="space-y-6">
      <h2 id="ledger" className="text-2xl font-bold">Rémunération — Grand livre</h2>
      <div className="card p-4">
        <table className="w-full text-left">
          <thead className="text-slate-600">
            <tr>
              <th className="py-2">Agent</th>
              <th>Source</th>
              <th>Montant</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {entries.map(e => {
              const a = agents.find(x => x.id === e.agentId)
              return (
                <tr key={e.id} className="border-t border-white/10">
                  <td className="py-2">{a?.name ?? e.agentId}</td>
                  <td>{e.source}</td>
                  <td>{(e.amount/100).toFixed(2)} DH</td>
                  <td>{e.status}</td>
                  <td className="flex gap-2 py-2">
                    <form action={mark}><input type="hidden" name="id" value={e.id} /><input type="hidden" name="status" value="invoiced" /><button className="btn btn-secondary">Préparer la facture</button></form>
                    <form action={mark}><input type="hidden" name="id" value={e.id} /><input type="hidden" name="status" value="paid_out" /><button className="btn btn-primary">Marquer comme payé</button></form>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
        </div>
      </div>
    </main>
  )
}
