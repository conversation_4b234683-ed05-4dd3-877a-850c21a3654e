@tailwind base;
@tailwind components;
@tailwind utilities;

html, body, :root { height: 100%; }
body { @apply bg-white text-slate-900; }

.card { @apply bg-white rounded-2xl shadow-soft border border-slate-200; }

.btn {
  @apply inline-flex items-center justify-center rounded-xl px-4 py-2 font-medium transition shadow-soft focus-visible:outline-none focus-visible:shadow-focus;
}
.btn-primary { @apply bg-brand-primary hover:bg-indigo-900 text-white; }
.btn-secondary { @apply bg-transparent border border-slate-300 hover:bg-slate-100 text-slate-800; }
.btn-sm { @apply px-3 py-1 text-sm rounded-lg; }
.input { @apply w-full rounded-xl bg-white text-slate-900 placeholder-slate-400 px-3 py-2 border border-slate-300 focus:outline-none focus:shadow-focus; }
.input-sm { @apply px-2 py-1 text-sm rounded-lg; }

.sidebar-link { @apply block px-3 py-2 rounded-lg text-slate-700 hover:bg-slate-100; }

/* Badges */
.badge { @apply inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium border; }
.badge-neutral { @apply bg-slate-100 text-slate-700 border-slate-200; }
.badge-success { @apply bg-green-100 text-green-700 border-green-200; }
.badge-warning { @apply bg-amber-100 text-amber-700 border-amber-200; }
.badge-info { @apply bg-blue-100 text-blue-700 border-blue-200; }
.badge-danger { @apply bg-rose-100 text-rose-700 border-rose-200; }

/* Table polish */
.table { @apply w-full text-left; }
.table th { @apply text-slate-600 font-medium; }
.table th, .table td { @apply py-2 align-top; }
.table tr + tr { @apply border-t border-slate-200; }
