# Deliverio — Pickup Module (Prototype)

Prototype implementing the PRD in `docs/PRD.md` with Next.js 14, <PERSON><PERSON><PERSON>, Prisma + SQLite, and a quick demo auth (role selector).

## Quickstart

1) Prereqs: Node 18+.

2) Configure env:

```
cp .env.example .env
```

3) Install deps:

```
npm install
```

4) Prisma init and migrate:

```
npx prisma init --datasource-provider sqlite
npm run prisma:migrate -- --name init
npm run prisma:generate
```

5) Seed demo data:

```
npm run seed
```

6) Run dev server:

```
npm run dev
```

Navigate to `/` to choose an account: Client, Agent de ramassage, Administrateur, Comptable.

## NPM Scripts

- `dev`, `build`, `start`, `lint`
- `prisma:migrate`, `prisma:generate`, `seed`

## API Endpoints (prototype)

- `POST /api/pickup-requests` — Customer creates a request
- `POST /api/pickup-requests/:id/accept` — Admin accepts
- `POST /api/pickup-requests/:id/reject` — Admin rejects with reason
- `POST /api/pickup-requests/:id/assign` — Admin assigns agent (moves to IN_PICKUP)
- `POST /api/pickup-requests/:id/schedule` — Admin/Customer schedules (moves to SCHEDULED)
- `POST /api/pickup-requests/:id/collect` — Agent marks collected (COMPLETED + ledger)
- `POST /api/pickup-agents/bulk-reassign` — Admin transactional reassignment
- `GET /api/pickup-compensation/ledger?agentId=&status=` — Accountant/Admin ledger view

## Notes

- UI in FR; code/DB in EN. Zod validation and FR error strings in endpoints.
- Capacity check is naive (global per-slot cap). Extend via `PickupAgentProfile.capacity` for finer control.
- Compensation ledger accrues per agent mode: per parcel, per delivery note, or monthly.
- Demo auth uses a cookie set by a role selector (no passwords).

