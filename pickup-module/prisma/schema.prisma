// datasource: sqlite (file:./dev.db)
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                 String              @id @default(cuid())
  name               String
  email              String              @unique
  role               String // 'CUSTOMER' | 'PICKUP_AGENT' | 'ADMIN' | 'ACCOUNTANT'
  pickupAgentProfile PickupAgentProfile?
  createdAt          DateTime            @default(now())
}

model PickupAgentProfile {
  id                 String   @id @default(cuid())
  userId             String   @unique
  user               User     @relation(fields: [userId], references: [id])
  enablePickup       Boolean  @default(false)
  enabledCustomerIds String? // JSON string array of customer IDs
  compensationMode   String   @default("PER_PARCEL") // 'PER_PARCEL' | 'PER_DELIVERY_NOTE' | 'MONTHLY'
  rates              String? // JSON string: { ratePerParcel?: number, perDeliveryNote?: number, monthly?: number }
  capacity           String? // JSON string e.g., { AM: 20, PM: 20 }
  suspended          Boolean  @default(false)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
}

model PickupAssignment {
  id         String   @id @default(cuid())
  customerId String
  agentId    String
  active     Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model PickupRequest {
  id              String    @id @default(cuid())
  customerId      String
  status          String    @default("REQUESTED") // 'REQUESTED' | 'ACCEPTED' | 'REJECTED' | 'SCHEDULED' | 'IN_PICKUP' | 'COMPLETED'
  preferredDate   DateTime?
  slotStart       DateTime?
  slotEnd         DateTime?
  agentId         String?
  cityId          String?
  itemsCount      Int       @default(1)
  rejectionReason String?
  createdAt       DateTime  @default(now())
}

model PickupCompensationLedger {
  id        String   @id @default(cuid())
  agentId   String
  source    String // 'parcel' | 'delivery_note' | 'monthly'
  sourceId  String?
  amount    Int // cents
  status    String // 'accruing'|'ready_to_invoice'|'invoiced'|'paid_out'
  period    String?
  meta      String? // JSON string
  createdAt DateTime @default(now())
}

// New city-based coverage and standing schedule
model City {
  id        String   @id @default(cuid())
  name      String   @unique
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
}

model PickupAgentCoverage {
  id        String   @id @default(cuid())
  agentId   String   // User.id for PICKUP_AGENT
  cityId    String
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
}

// Customer daily pickup schedule (standing pickup)
model PickupSchedule {
  id         String   @id @default(cuid())
  customerId String
  cityId     String
  timeSlot   String   // 'AM' | 'PM'
  active     Boolean  @default(true)
  createdAt  DateTime @default(now())
}

// Delivery Note (prototype) — creating a DN triggers/links a pickup when schedule exists
model DeliveryNote {
  id         String   @id @default(cuid())
  customerId String
  cityId     String
  itemsCount Int      @default(1)
  status     String   @default("NEW")
  createdAt  DateTime @default(now())
}
