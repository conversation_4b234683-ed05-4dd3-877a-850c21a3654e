import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Demo users
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Client Démo', email: '<EMAIL>', role: 'CUSTOMER' }
  })

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Administrateur', email: '<EMAIL>', role: 'ADMIN' }
  })

  const accountant = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Comptable', email: '<EMAIL>', role: 'ACCOUNTANT' }
  })

  const agentUserA = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Agent <PERSON>', email: '<EMAIL>', role: 'PICKUP_AGENT' }
  })

  const agentUserB = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Agent B', email: '<EMAIL>', role: 'PICKUP_AGENT' }
  })

  // Agent profiles
  await prisma.pickupAgentProfile.upsert({
    where: { userId: agentUserA.id },
    update: {},
    create: {
      userId: agentUserA.id,
      enablePickup: true,
      compensationMode: 'PER_PARCEL',
      rates: JSON.stringify({ ratePerParcel: 500 }),
      capacity: JSON.stringify({ AM: 20, PM: 20 }),
      enabledCustomerIds: JSON.stringify([customer.id])
    }
  })

  await prisma.pickupAgentProfile.upsert({
    where: { userId: agentUserB.id },
    update: {},
    create: {
      userId: agentUserB.id,
      enablePickup: true,
      compensationMode: 'PER_DELIVERY_NOTE',
      rates: JSON.stringify({ perDeliveryNote: 2000 }),
      capacity: JSON.stringify({ AM: 10, PM: 15 }),
      enabledCustomerIds: JSON.stringify([customer.id])
    }
  })

  // A sample pending pickup request
  await prisma.pickupRequest.create({
    data: {
      customerId: customer.id,
      status: 'REQUESTED',
      itemsCount: 3,
      preferredDate: new Date()
    } as any
  })

  console.log('Seed complete:', { customer, admin, accountant, agentUserA, agentUserB })
}

main().catch((e) => {
  console.error(e)
  process.exit(1)
}).finally(async () => {
  await prisma.$disconnect()
})
