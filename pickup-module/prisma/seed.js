// Plain JS seed to avoid ts-node ESM issues
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Client Démo', email: '<EMAIL>', role: 'CUSTOMER' }
  })

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Administrateur', email: '<EMAIL>', role: 'ADMIN' }
  })

  const accountant = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Comptable', email: '<EMAIL>', role: 'ACCOUNTANT' }
  })

  const agentUserA = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Agent <PERSON>', email: 'agent<PERSON>@example.com', role: 'PICKUP_AGENT' }
  })

  const agentUserB = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { name: 'Agent B', email: '<EMAIL>', role: 'PICKUP_AGENT' }
  })

  await prisma.pickupAgentProfile.upsert({
    where: { userId: agentUserA.id },
    update: {},
    create: {
      userId: agentUserA.id,
      enablePickup: true,
      compensationMode: 'PER_PARCEL',
      rates: JSON.stringify({ ratePerParcel: 500 }),
      capacity: JSON.stringify({ AM: 20, PM: 20 }),
      enabledCustomerIds: JSON.stringify([customer.id])
    }
  })

  await prisma.pickupAgentProfile.upsert({
    where: { userId: agentUserB.id },
    update: {},
    create: {
      userId: agentUserB.id,
      enablePickup: true,
      compensationMode: 'PER_DELIVERY_NOTE',
      rates: JSON.stringify({ perDeliveryNote: 2000 }),
      capacity: JSON.stringify({ AM: 10, PM: 15 }),
      enabledCustomerIds: JSON.stringify([customer.id])
    }
  })

  await prisma.pickupRequest.create({
    data: {
      customerId: customer.id,
      status: 'REQUESTED',
      itemsCount: 3,
      preferredDate: new Date()
    }
  })

  // Cities and coverage
  const casa = await prisma.city.upsert({ where: { name: 'Casablanca' }, update: {}, create: { name: 'Casablanca', active: true } })
  const rabat = await prisma.city.upsert({ where: { name: 'Rabat' }, update: {}, create: { name: 'Rabat', active: true } })

  await prisma.pickupAgentCoverage.createMany({
    data: [
      { agentId: agentUserA.id, cityId: casa.id, active: true },
      { agentId: agentUserB.id, cityId: casa.id, active: true },
      { agentId: agentUserB.id, cityId: rabat.id, active: true },
    ],
    skipDuplicates: true,
  })

  // Default customer schedule (daily AM in Casablanca)
  await prisma.pickupSchedule.create({
    data: { customerId: customer.id, cityId: casa.id, timeSlot: 'AM', active: true }
  })

  console.log('Seed complete:', { customer: customer.email, admin: admin.email, accountant: accountant.email, agentA: agentUserA.email, agentB: agentUserB.email, cities: ['Casablanca','Rabat'] })
}

main().catch((e) => {
  console.error(e)
  process.exit(1)
}).finally(async () => {
  await prisma.$disconnect()
})
